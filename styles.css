/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background: #f5f7fa;
    min-height: 100vh;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 600;
}

/* 主内容区域 */
.main-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #e1e8ed;
}

/* 文件上传区域 */
.upload-section {
    margin-bottom: 25px;
}

.upload-area {
    border: 2px dashed #3498db;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    background: #f8fbff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-area:hover {
    border-color: #2980b9;
    background: #f0f8ff;
}

.upload-area.dragover {
    border-color: #2980b9;
    background: #e8f4fd;
}

.upload-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.upload-icon {
    font-size: 2.5rem;
    color: #3498db;
}

.upload-text h3 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1.2rem;
    font-weight: 600;
}

.upload-text p {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    pointer-events: none;
}

.upload-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.upload-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

/* 文件信息显示 */
.file-info {
    background: #e8f5e8;
    border-radius: 6px;
    padding: 15px 20px;
    margin-top: 15px;
    border-left: 4px solid #27ae60;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.file-details {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1rem;
    margin-bottom: 4px;
}

.file-meta {
    font-size: 0.85rem;
    color: #7f8c8d;
}

.reprocess-btn {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reprocess-btn:hover {
    background: #7f8c8d;
}

/* 消息区域 */
.message {
    padding: 12px 16px;
    border-radius: 6px;
    margin-top: 20px;
    display: none;
    font-size: 0.9rem;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* 月视图样式 */
.month-view-section {
    margin-top: 25px;
    padding: 20px;
    background: #f8fafe;
    border-radius: 8px;
    border: 1px solid #e1e8ed;
}

.month-view-header {
    margin-bottom: 20px;
}

.stats-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e1e8ed;
    font-size: 0.9rem;
}

.stats-item {
    color: #2c3e50;
}

.stats-item strong {
    color: #3498db;
}

.selected-count {
    color: #e74c3c;
}

/* 月历导航 */
.calendar-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.nav-btn {
    background: #3498db;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #2980b9;
}

.current-month {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
    text-align: center;
}

.clear-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    background: #c0392b;
}

/* 月历容器 */
.calendar-container {
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e1e8ed;
    margin-bottom: 15px;
}

/* 月历表格 */
.calendar-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.calendar-table th,
.calendar-table td {
    width: 14.28%;
    height: 40px;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #f1f3f4;
    position: relative;
}

.calendar-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    height: 35px;
    font-size: 0.85rem;
}

.calendar-date {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 3px;
    margin: 1px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-size: 0.9rem;
}

.calendar-date:hover {
    background: #e8f4fd;
}

.calendar-date.other-month {
    color: #bdc3c7;
    cursor: default;
}

.calendar-date.other-month:hover {
    background: transparent;
}

.calendar-date.has-data {
    background: #e8f5e8;
    font-weight: 600;
    color: #27ae60;
}

.calendar-date.has-data:hover {
    background: #d5e7d5;
}

.calendar-date.selected {
    background: #3498db !important;
    color: white;
    font-weight: 600;
}

.calendar-date.selected:hover {
    background: #2980b9 !important;
}

/* 底部区域 */
.bottom-section {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.selected-dates {
    flex: 1;
    background: white;
    border-radius: 6px;
    padding: 15px;
    border: 1px solid #e1e8ed;
}

.selected-dates-list {
    min-height: 40px;
    max-height: 120px;
    overflow-y: auto;
}

.no-selection {
    color: #95a5a6;
    font-size: 0.9rem;
    text-align: center;
    margin: 0;
    padding: 10px 0;
}

.selected-date-item {
    display: inline-block;
    background: #3498db;
    color: white;
    padding: 4px 10px;
    margin: 2px;
    border-radius: 12px;
    font-size: 0.85rem;
    position: relative;
}

.selected-date-item .remove-date {
    margin-left: 6px;
    cursor: pointer;
    font-weight: bold;
    opacity: 0.8;
}

.selected-date-item .remove-date:hover {
    opacity: 1;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.preview-btn {
    background: #f39c12;
    color: white;
}

.preview-btn:hover {
    background: #e67e22;
}

.export-btn {
    background: #27ae60;
    color: white;
}

.export-btn:hover:not(:disabled) {
    background: #229954;
}

.export-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .main-content {
        padding: 20px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .upload-content {
        flex-direction: column;
        gap: 15px;
    }

    .upload-text {
        text-align: center;
    }

    .stats-summary {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .calendar-navigation {
        flex-wrap: wrap;
        gap: 10px;
    }

    .current-month {
        order: -1;
        width: 100%;
    }

    .calendar-table th,
    .calendar-table td {
        height: 35px;
        font-size: 0.8rem;
    }

    .calendar-date {
        height: 30px;
        font-size: 0.8rem;
    }

    .bottom-section {
        flex-direction: column;
        gap: 15px;
    }

    .action-buttons {
        flex-direction: row;
        justify-content: center;
    }

    .action-btn {
        min-width: 100px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-info,
.month-view-section {
    animation: fadeIn 0.4s ease;
}
